import type { TreeNode } from "./template-tree-parser";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function evaluateTree(data: any, tree: TreeNode): string | null {
  switch (tree.type) {
    case "value": {
      return tree.value;
    }

    case "reference": {
      if (tree.name in data) {
        return data[tree.name];
      }

      return null;
    }

    case "ternary": {
      const value = tree.condition in data ? data[tree.condition] : null;

      return value
        ? evaluateTree(data, tree.true)
        : evaluateTree(data, tree.false);
    }

    case "nullish": {
      for (const node of tree.values) {
        const value = evaluateTree(data, node);

        if (value) {
          return value;
        }
      }

      return null;
    }

    case "list": {
      const values = tree.values.map((node) => evaluateTree(data, node));

      if (values.every((v) => !!v)) {
        return values.join("");
      }

      return null;
    }
  }
}
