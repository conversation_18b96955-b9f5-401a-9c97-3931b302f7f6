import type { TreeNode } from "./template-tree-parser";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function evaluateTree(data: any, tree: TreeNode): string | null {
  switch (tree.type) {
    case "value": {
      return tree.value;
    }

    case "reference": {
      if (tree.name in data) {
        return data[tree.name];
      }

      return null;
    }

    case "ternary": {
      const conditionValue = evaluateTree(data, tree.condition);

      // Evaluate truthiness: non-empty strings and non-null values are truthy
      const isTruthy = conditionValue !== null && conditionValue !== "" && conditionValue !== "false";

      return isTruthy
        ? evaluateTree(data, tree.true)
        : evaluateTree(data, tree.false);
    }

    case "nullish": {
      for (const node of tree.values) {
        const value = evaluateTree(data, node);

        if (value) {
          return value;
        }
      }

      return null;
    }

    case "list": {
      const values = tree.values.map((node) => evaluateTree(data, node));

      if (values.every((v) => !!v)) {
        return values.join("");
      }

      return null;
    }

    case "boolean": {
      if (tree.operator === "AND") {
        // For AND: all operands must be truthy
        for (const operand of tree.operands) {
          const value = evaluateTree(data, operand);
          const isTruthy = value !== null && value !== "" && value !== "false";
          if (!isTruthy) {
            return null; // Short-circuit on first falsy value
          }
        }
        return "true"; // All operands are truthy
      } else if (tree.operator === "OR") {
        // For OR: at least one operand must be truthy
        for (const operand of tree.operands) {
          const value = evaluateTree(data, operand);
          const isTruthy = value !== null && value !== "" && value !== "false";
          if (isTruthy) {
            return "true"; // Short-circuit on first truthy value
          }
        }
        return null; // No operands are truthy
      }
      return null;
    }

    case "plus": {
      // Concatenate all operands with String() conversion
      const values = tree.operands.map((operand) => {
        const value = evaluateTree(data, operand);
        return value !== null ? String(value) : "";
      });

      return values.join("");
    }
  }
}
