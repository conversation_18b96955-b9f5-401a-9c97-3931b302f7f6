// tree-parser.ts
/** AST types (from user) */
export type TernaryConditionNode = {
  type: "ternary";
  condition: TreeNode; // Changed from string to TreeNode to support complex conditions
  true: TreeNode;
  false: TreeNode;
};
export type NullishCoalescingNode = {
  type: "nullish";
  values: TreeNode[];
};
export type ListNode = {
  type: "list";
  values: TreeNode[];
};
export type ValueNode = {
  type: "value";
  value: string;
};
export type ReferenceNode = {
  type: "reference";
  name: string;
};
export type BooleanExpressionNode = {
  type: "boolean";
  operator: "AND" | "OR";
  operands: TreeNode[];
};
export type PlusExpressionNode = {
  type: "plus";
  operands: TreeNode[];
};

export type TreeNode =
  | TernaryConditionNode
  | NullishCoalescingNode
  | ListNode
  | ValueNode
  | ReferenceNode
  | BooleanExpressionNode
  | PlusExpressionNode;

/* ============================
   Lexer
   ============================ */

type Token =
  | { type: "BRACE_OPEN"; pos: number }
  | { type: "BRACE_CLOSE"; pos: number }
  | { type: "QUESTION"; pos: number }
  | { type: "COLON"; pos: number }
  | { type: "COMMA"; pos: number }
  | { type: "NULLISH"; pos: number } // '??'
  | { type: "AND"; pos: number }
  | { type: "OR"; pos: number }
  | { type: "PLUS"; pos: number }
  | { type: "IDENT"; value: string; pos: number }
  | { type: "STRING"; value: string; pos: number }
  | { type: "EOF"; pos: number };

class Lexer {
  private i = 0;
  private input: string;
  constructor(input: string) {
    this.input = input;
  }

  nextToken(): Token {
    const s = this.input;
    const n = s.length;
    while (this.i < n) {
      const ch = s[this.i];

      // skip whitespace
      if (/\s/.test(ch)) {
        this.i++;
        continue;
      }

      const pos = this.i;

      // punctuation and operators
      if (ch === "{") {
        this.i++;
        return { type: "BRACE_OPEN", pos };
      }
      if (ch === "}") {
        this.i++;
        return { type: "BRACE_CLOSE", pos };
      }
      if (ch === "?") {
        // could be '??' or '?'
        if (s[this.i + 1] === "?") {
          this.i += 2;
          return { type: "NULLISH", pos };
        } else {
          this.i++;
          return { type: "QUESTION", pos };
        }
      }
      if (ch === ":") {
        this.i++;
        return { type: "COLON", pos };
      }
      if (ch === ",") {
        this.i++;
        return { type: "COMMA", pos };
      }
      if (ch === "+") {
        this.i++;
        return { type: "PLUS", pos };
      }

      // string literal (double quotes)
      if (ch === '"') {
        this.i++;
        let out = "";
        while (this.i < n) {
          const c = s[this.i++];
          if (c === '"') break;
          if (c === "\\") {
            if (this.i >= n) throw this.error("Unterminated string literal", this.i - 1);
            const esc = s[this.i++];
            switch (esc) {
              case "n":
                out += "\n";
                break;
              case "r":
                out += "\r";
                break;
              case "t":
                out += "\t";
                break;
              case "\\":
                out += "\\";
                break;
              case '"':
                out += '"';
                break;
              default:
                out += esc; // keep unknown escapes as-is
            }
          } else {
            out += c;
          }
        }
        return { type: "STRING", value: out, pos };
      }

      // identifier: letters, digits, underscore, hyphen? (we'll allow underscore and digits after first)
      if (/[A-Za-z_.]/.test(ch)) {
        const start = this.i;
        this.i++;
        while (this.i < n && /[A-Za-z0-9_.]/.test(s[this.i])) this.i++;
        const name = s.slice(start, this.i);

        // Check for keywords
        if (name === "AND") {
          return { type: "AND", pos };
        }
        if (name === "OR") {
          return { type: "OR", pos };
        }

        return { type: "IDENT", value: name, pos };
      }

      // unknown char
      throw this.error(`Unexpected character '${ch}'`, this.i);
    }

    return { type: "EOF", pos: this.i };
  }

  private error(msg: string, pos: number): Error {
    return new SyntaxError(`${msg} at position ${pos}`);
  }
}

/* ============================
   Parser (recursive-descent)
   ============================ */

class Parser {
  private tokens: Token[] = [];
  private idx = 0;

  constructor(input: string) {
    // initialize tokens by lexing whole input
    const lexer = new Lexer(input);
    let t: Token;
    do {
      t = lexer.nextToken();
      this.tokens.push(t);
    } while (t.type !== "EOF");
  }

  parse(): TreeNode {
    // Accept optional leading/trailing text: try to locate first '{' and parse its content;
    // but prefer parsing whole input as expression too.
    // We'll parse a top-level expression (which may be braced group).
    const node = this.parseExpression();

    this.expectEOF();
    return node;
  }

  private current(): Token {
    return this.tokens[this.idx];
  }
  private consume(): Token {
    return this.tokens[this.idx++];
  }
  private expect(type: Token["type"]): Token {
    const tok = this.current();
    if (tok.type !== type) throw new SyntaxError(`Expected ${type} but got ${tok.type} at pos ${tok.pos}`);
    return this.consume();
  }
  private accept(type: Token["type"]): Token | null {
    const tok = this.current();
    if (tok.type === type) return this.consume();
    return null;
  }
  private expectEOF() {
    const t = this.current();
    if (t.type !== "EOF") {
      throw new SyntaxError(`Expected EOF but got ${t.type} at pos ${t.pos}`);
    }
  }

  /* Top-level expression parser.
     New priority/resolution strategy:
     1. Check for ternary: expr '?' expr ':' expr (where expr can be any expression including boolean/plus)
     2. Otherwise parse OR expression (lowest precedence)

     Operator precedence (highest to lowest):
     - Primary (identifiers, strings, braced expressions)
     - AND
     - OR
     - Plus (+)
     - Nullish (??)
     - Ternary (? :)
  */
  private parseExpression(): TreeNode {
    return this.parseTernary();
  }

  private parseTernary(): TreeNode {
    const condition = this.parseNullishChain();

    if (this.accept("QUESTION")) {
      const trueNode = this.parseExpressionOrWrapped();
      this.expect("COLON");
      const falseNode = this.parseExpressionOrWrapped();
      return {
        type: "ternary",
        condition: condition,
        true: trueNode,
        false: falseNode,
      } as TernaryConditionNode;
    }

    return condition;
  }



  // parses expression but allows braced grouping as a primary location
  private parseExpressionOrWrapped(): TreeNode {
    // if next token is BRACE_OPEN, parse that block (which might be list or inner expr)
    if (this.current().type === "BRACE_OPEN") {
      return this.parseBraced();
    }
    // otherwise parse full expression from here
    return this.parseExpression();
  }

  private parseNullishChain(): TreeNode {
    const first = this.parsePlusExpression();

    const values: TreeNode[] = [first];
    while (this.accept("NULLISH")) {
      // after '??' the next expression can be braced or normal
      const nxt = this.parsePlusExpression();
      values.push(nxt);
    }

    if (values.length === 1) return first;
    return { type: "nullish", values } as NullishCoalescingNode;
  }

  private parsePlusExpression(): TreeNode {
    const first = this.parseOrExpression();

    // Check if this is a plus expression
    if (this.current().type === "PLUS") {
      const operands: TreeNode[] = [first];

      while (this.accept("PLUS")) {
        const next = this.parseOrExpression();
        operands.push(next);
      }

      // Check for mixed operators at this level
      this.validateNoMixedOperators("PLUS");

      return { type: "plus", operands } as PlusExpressionNode;
    }

    return first;
  }

  private validateNoMixedOperators(currentOperator: string) {
    const token = this.current();
    if (token.type === "AND" || token.type === "OR" || token.type === "PLUS" || token.type === "NULLISH") {
      if (token.type !== currentOperator) {
        throw new SyntaxError(`Cannot mix ${currentOperator} operator with ${token.type} at the same level at position ${token.pos}. Use braces to group expressions.`);
      }
    }
  }

  private parseOrExpression(): TreeNode {
    const first = this.parseAndExpression();

    // Check if this is an OR expression
    if (this.current().type === "OR") {
      // Check if the first operand is a plus expression - this is not allowed
      if (first.type === "plus") {
        throw new SyntaxError(`Cannot mix + operator with OR at the same level at position ${this.current().pos}. Use braces to group expressions.`);
      }

      const operands: TreeNode[] = [first];

      while (this.accept("OR")) {
        const next = this.parseAndExpression();
        operands.push(next);
      }

      // Check for mixed operators at this level
      this.validateNoMixedOperators("OR");

      return { type: "boolean", operator: "OR", operands } as BooleanExpressionNode;
    }

    return first;
  }

  private parseAndExpression(): TreeNode {
    const first = this.parsePrimary();

    // Check if this is an AND expression
    if (this.current().type === "AND") {
      const operands: TreeNode[] = [first];

      while (this.accept("AND")) {
        const next = this.parsePrimary();
        operands.push(next);
      }

      // Check for mixed operators at this level
      this.validateNoMixedOperators("AND");

      return { type: "boolean", operator: "AND", operands } as BooleanExpressionNode;
    }

    return first;
  }

  private parsePrimary(): TreeNode {
    const tok = this.current();
    switch (tok.type) {
      case "STRING":
        this.consume();
        return { type: "value", value: (tok as { type: "STRING"; value: string }).value } as ValueNode;

      case "IDENT":
        this.consume();
        return { type: "reference", name: (tok as { type: "IDENT"; value: string }).value } as ReferenceNode;

      case "BRACE_OPEN":
        return this.parseBraced();

      default:
        throw new SyntaxError(`Unexpected token ${tok.type} at pos ${tok.pos}`);
    }
  }

  /** Parses a braced block { ... }.
      If the top-level content inside braces contains commas (multiple top-level items),
      we return a ListNode. Otherwise return the single contained node.
  */
  private parseBraced(): TreeNode {
    this.expect("BRACE_OPEN");

    // check for empty braces
    if (this.accept("BRACE_CLOSE")) {
      // empty list
      return { type: "list", values: [] } as ListNode;
    }

    // parse first expression inside braces - use parseExpression to get full expression parsing
    const first = this.parseExpression();
    const values: TreeNode[] = [first];

    // if there are commas at the top-level, it's a list: collect all items separated by commas
    while (this.accept("COMMA")) {
      // allow trailing comma before closing
      if (this.current().type === "BRACE_CLOSE") break;
      const item = this.parseExpression();
      values.push(item);
    }

    // expect closing brace
    this.expect("BRACE_CLOSE");

    if (values.length === 1) {
      // single entry: return the single node (no extra list wrapper)
      return values[0];
    }

    return { type: "list", values } as ListNode;
  }
}

/* ============================
   Public API
   ============================ */

/**
 * Parse a tree expression string into TreeNode.
 *
 * Acceptable input forms:
 * - A braced expression: `{a ? "b" : {c ? "d" : "e"}}`
 * - A plain expression: `a ?? b ?? "c"`
 * - A braced list: `{a, b, "c", {d ? "e" : "f"}}`
 *
 * Throws SyntaxError on invalid input.
 */
export function parseTree(input: string): TreeNode {
  // Quick trim
  const trimmed = input.trim();

  // If the user embedded the expression in other text like "ternary condition { ... }",
  // attempt to find the *first* brace pair and parse inner content. This helps be forgiving
  // with examples that include labels.
  if (!trimmed.startsWith("{")) {
    // find first '{' and last matching '}' if exists and contains more text after it.
    const firstBrace = trimmed.indexOf("{");
    if (firstBrace >= 0) {
      // naive: parse from first brace to matching closing brace by scanning
      let depth = 0;
      let matchIdx = -1;
      for (let i = firstBrace; i < trimmed.length; i++) {
        if (trimmed[i] === "{") depth++;
        else if (trimmed[i] === "}") {
          depth--;
          if (depth === 0) {
            matchIdx = i;
            break;
          }
        }
      }
      if (matchIdx >= 0) {
        const inner = trimmed.slice(firstBrace, matchIdx + 1);
        const parser = new Parser(inner);
        return parser.parse();
      }
    }
  }

  const parser = new Parser(trimmed);
  return parser.parse();
}
