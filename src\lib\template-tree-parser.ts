// tree-parser.ts
/** AST types (from user) */
export type TernaryConditionNode = {
  type: "ternary";
  condition: string;
  true: TreeNode;
  false: TreeNode;
};
export type NullishCoalescingNode = {
  type: "nullish";
  values: TreeNode[];
};
export type ListNode = {
  type: "list";
  values: TreeNode[];
};
export type ValueNode = {
  type: "value";
  value: string;
};
export type ReferenceNode = {
  type: "reference";
  name: string;
};

export type TreeNode =
  | TernaryConditionNode
  | NullishCoalescingNode
  | ListNode
  | ValueNode
  | ReferenceNode;

/* ============================
   Lexer
   ============================ */

type Token =
  | { type: "BRACE_OPEN"; pos: number }
  | { type: "BRACE_CLOSE"; pos: number }
  | { type: "QUESTION"; pos: number }
  | { type: "COLON"; pos: number }
  | { type: "COMMA"; pos: number }
  | { type: "NULLISH"; pos: number } // '??'
  | { type: "IDENT"; value: string; pos: number }
  | { type: "STRING"; value: string; pos: number }
  | { type: "EOF"; pos: number };

class Lexer {
  private i = 0;
  private input: string;
  constructor(input: string) {
    this.input = input;
  }

  nextToken(): Token {
    const s = this.input;
    const n = s.length;
    while (this.i < n) {
      const ch = s[this.i];

      // skip whitespace
      if (/\s/.test(ch)) {
        this.i++;
        continue;
      }

      const pos = this.i;

      // punctuation and operators
      if (ch === "{") {
        this.i++;
        return { type: "BRACE_OPEN", pos };
      }
      if (ch === "}") {
        this.i++;
        return { type: "BRACE_CLOSE", pos };
      }
      if (ch === "?") {
        // could be '??' or '?'
        if (s[this.i + 1] === "?") {
          this.i += 2;
          return { type: "NULLISH", pos };
        } else {
          this.i++;
          return { type: "QUESTION", pos };
        }
      }
      if (ch === ":") {
        this.i++;
        return { type: "COLON", pos };
      }
      if (ch === ",") {
        this.i++;
        return { type: "COMMA", pos };
      }

      // string literal (double quotes)
      if (ch === '"') {
        this.i++;
        let out = "";
        while (this.i < n) {
          const c = s[this.i++];
          if (c === '"') break;
          if (c === "\\") {
            if (this.i >= n) throw this.error("Unterminated string literal", this.i - 1);
            const esc = s[this.i++];
            switch (esc) {
              case "n":
                out += "\n";
                break;
              case "r":
                out += "\r";
                break;
              case "t":
                out += "\t";
                break;
              case "\\":
                out += "\\";
                break;
              case '"':
                out += '"';
                break;
              default:
                out += esc; // keep unknown escapes as-is
            }
          } else {
            out += c;
          }
        }
        return { type: "STRING", value: out, pos };
      }

      // identifier: letters, digits, underscore, hyphen? (we'll allow underscore and digits after first)
      if (/[A-Za-z_.]/.test(ch)) {
        const start = this.i;
        this.i++;
        while (this.i < n && /[A-Za-z0-9_.]/.test(s[this.i])) this.i++;
        const name = s.slice(start, this.i);
        return { type: "IDENT", value: name, pos };
      }

      // unknown char
      throw this.error(`Unexpected character '${ch}'`, this.i);
    }

    return { type: "EOF", pos: this.i };
  }

  private error(msg: string, pos: number): Error {
    return new SyntaxError(`${msg} at position ${pos}`);
  }
}

/* ============================
   Parser (recursive-descent)
   ============================ */

class Parser {
  private tokens: Token[] = [];
  private idx = 0;

  constructor(input: string) {
    // initialize tokens by lexing whole input
    const lexer = new Lexer(input);
    let t: Token;
    do {
      t = lexer.nextToken();
      this.tokens.push(t);
    } while (t.type !== "EOF");
  }

  parse(): TreeNode {
    // Accept optional leading/trailing text: try to locate first '{' and parse its content;
    // but prefer parsing whole input as expression too.
    // We'll parse a top-level expression (which may be braced group).
    const node = this.parseExpression();

    this.expectEOF();
    return node;
  }

  private current(): Token {
    return this.tokens[this.idx];
  }
  private consume(): Token {
    return this.tokens[this.idx++];
  }
  private expect(type: Token["type"]): Token {
    const tok = this.current();
    if (tok.type !== type) throw new SyntaxError(`Expected ${type} but got ${tok.type} at pos ${tok.pos}`);
    return this.consume();
  }
  private accept(type: Token["type"]): Token | null {
    const tok = this.current();
    if (tok.type === type) return this.consume();
    return null;
  }
  private expectEOF() {
    const t = this.current();
    if (t.type !== "EOF") {
      throw new SyntaxError(`Expected EOF but got ${t.type} at pos ${t.pos}`);
    }
  }

  /* Top-level expression parser.
     Priority/resolution strategy:
     - If we see an IDENT followed by '?', we parse a ternary (condition = that ident).
     - Otherwise parse a nullish chain (primary ('??' primary)*).
     Primary may be a braced expression (which internally may produce list/ternary/nullish)
  */
  private parseExpression(): TreeNode {
    // ternary: IDENT '?' expr ':' expr
    const cur = this.current();
    if (cur.type === "IDENT") {
      const next = this.tokens[this.idx + 1];
      if (next && next.type === "QUESTION") {
        return this.parseTernary();
      }
    }

    // else parse nullish chain (which delegates to primary)
    return this.parseNullishChain();
  }

  private parseTernary(): TreeNode {
    // condition is a bare identifier (by design)
    const condTok = this.expect("IDENT") as { type: "IDENT"; value: string; pos: number };
    this.expect("QUESTION");
    const trueNode = this.parseExpressionOrWrapped();
    this.expect("COLON");
    const falseNode = this.parseExpressionOrWrapped();
    return {
      type: "ternary",
      condition: condTok.value,
      true: trueNode,
      false: falseNode,
    } as TernaryConditionNode;
  }

  // parses expression but allows braced grouping as a primary location
  private parseExpressionOrWrapped(): TreeNode {
    // if next token is BRACE_OPEN, parse that block (which might be list or inner expr)
    if (this.current().type === "BRACE_OPEN") {
      return this.parseBraced();
    }
    // otherwise parse full expression from here
    return this.parseExpression();
  }

  private parseNullishChain(): TreeNode {
    const first = this.parsePrimary();

    const values: TreeNode[] = [first];
    while (this.accept("NULLISH")) {
      // after '??' the next primary can be braced or normal
      const nxt = this.parsePrimary();
      values.push(nxt);
    }

    if (values.length === 1) return first;
    return { type: "nullish", values } as NullishCoalescingNode;
  }

  private parsePrimary(): TreeNode {
    const tok = this.current();
    switch (tok.type) {
      case "STRING":
        this.consume();
        return { type: "value", value: (tok as { type: "STRING"; value: string }).value } as ValueNode;

      case "IDENT":
        this.consume();
        return { type: "reference", name: (tok as { type: "IDENT"; value: string }).value } as ReferenceNode;

      case "BRACE_OPEN":
        return this.parseBraced();

      default:
        throw new SyntaxError(`Unexpected token ${tok.type} at pos ${tok.pos}`);
    }
  }

  /** Parses a braced block { ... }.
      If the top-level content inside braces contains commas (multiple top-level items),
      we return a ListNode. Otherwise return the single contained node.
  */
  private parseBraced(): TreeNode {
    this.expect("BRACE_OPEN");

    // check for empty braces
    if (this.accept("BRACE_CLOSE")) {
      // empty list
      return { type: "list", values: [] } as ListNode;
    }

    // parse first expression inside braces
    const first = this.parseExpressionOrWrapped();
    const values: TreeNode[] = [first];

    // if there are commas at the top-level, it's a list: collect all items separated by commas
    while (this.accept("COMMA")) {
      // allow trailing comma before closing
      if (this.current().type === "BRACE_CLOSE") break;
      const item = this.parseExpressionOrWrapped();
      values.push(item);
    }

    // expect closing brace
    this.expect("BRACE_CLOSE");

    if (values.length === 1) {
      // single entry: return the single node (no extra list wrapper)
      return values[0];
    }

    return { type: "list", values } as ListNode;
  }
}

/* ============================
   Public API
   ============================ */

/**
 * Parse a tree expression string into TreeNode.
 *
 * Acceptable input forms:
 * - A braced expression: `{a ? "b" : {c ? "d" : "e"}}`
 * - A plain expression: `a ?? b ?? "c"`
 * - A braced list: `{a, b, "c", {d ? "e" : "f"}}`
 *
 * Throws SyntaxError on invalid input.
 */
export function parseTree(input: string): TreeNode {
  // Quick trim
  const trimmed = input.trim();

  // If the user embedded the expression in other text like "ternary condition { ... }",
  // attempt to find the *first* brace pair and parse inner content. This helps be forgiving
  // with examples that include labels.
  if (!trimmed.startsWith("{")) {
    // find first '{' and last matching '}' if exists and contains more text after it.
    const firstBrace = trimmed.indexOf("{");
    if (firstBrace >= 0) {
      // naive: parse from first brace to matching closing brace by scanning
      let depth = 0;
      let matchIdx = -1;
      for (let i = firstBrace; i < trimmed.length; i++) {
        if (trimmed[i] === "{") depth++;
        else if (trimmed[i] === "}") {
          depth--;
          if (depth === 0) {
            matchIdx = i;
            break;
          }
        }
      }
      if (matchIdx >= 0) {
        const inner = trimmed.slice(firstBrace, matchIdx + 1);
        const parser = new Parser(inner);
        return parser.parse();
      }
    }
  }

  const parser = new Parser(trimmed);
  return parser.parse();
}
