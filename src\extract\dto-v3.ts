import { z } from "zod";
import * as Lists from "../shared/lists";

const string = z.string().nullable();
const number = z.number().nullable();

/*
  service: typeof Lists.services[number] | null;
  incoterms: typeof Lists.incoterms[number] | null;

  origin: {
    country: string | null;
    city: string | null;
    address: string | null;
    zipcode: string | null;
    isPickupRequired: boolean;
  };

  destination: {
    country: string | null;
    city: string | null;
    address: string | null;
    zipcode: string | null;
    isDeliveryRequired: boolean;
  };
*/

export const ExtractMetadata = z.object({
    service: z.enum(Lists.services).nullable(),
    incoterms: z.enum(Lists.incoterms).nullable(),

    origin: z.object({
        country: string,
        city: string,
        zipCode: string,
        address: string,
        isPickupRequired: z.boolean(),
    }),

    destination: z.object({
        country: string,
        city: string,
        zipCode: string,
        address: string,
        isDeliveryRequired: z.boolean(),
    }),
});

export const ExtractDigits = z.object({
    packages: z.array(
        z.object({
            quantity: number,
            weight: number,
            length: number,
            width: number,
            height: number,
            type: z.enum(Lists.packageTypes),
            isStackable: z.boolean(),
        }),
    ),

    total: z.object({
        quantity: number,
        weight: number,
        volume: number,
    }),
});

export const ExtractServices = z.object({
    descriptionOfGoods: string,
    hsCodes: z.array(z.string().nonempty()),

    costOfGoods: number,
    currency: number,

    additionalServices: z.array(z.enum(Lists.additionalServices)),
    dangerousGoods: z.array(z.enum(Lists.dangerousGoods)),
});
