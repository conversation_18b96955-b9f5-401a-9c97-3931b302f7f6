// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { useState } from "react";
import { ShippingInfo } from "../types/shippingTypes";
import { UseFormSetError } from "react-hook-form";
import { FormData } from "../types/shippingTypes";
import { Requester } from "../../../lib/requester";
import { Api } from "../../../shared";

// @ts-expect-error currently not used
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const useSaveRequest = ({ handleFieldErrors }: {
  handleFieldErrors: UseFormSetError<FormData>
}) => {
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const saveRequest = async (data: ShippingInfo) => {
    setError(null);
    setSuccess(null);

    try {
      console.dir(data, { depth: null });

      const response = await Requester.post("/request", {
        origin: data.origin ?? "unknown",
        destination: data.destination_country ?? "unknown",
        service: (data.service_type ?? "air") as Api.CreateRequestRequest["service"],
        quantity: data.summary.piece,
        weight: data.summary.weight,
        volume: data.summary.volume,
        chargeableWeight: data.summary.chargeable_weight ?? 0,
      } satisfies Api.CreateRequestRequest);

      if (response.success) {
        setSuccess("Successfully saved");

        return;
      }

      if (response.error) {
        setError(response.error.message);

        return;
      }

      setError(`Response failed with status ${response.status}: ${response.body}`);
    } catch (e) {
      setError("An unexpected error occurred. Please try again." + ` (${String(e)})`);
    }
  };

  return { saveRequest, error, success };
};
