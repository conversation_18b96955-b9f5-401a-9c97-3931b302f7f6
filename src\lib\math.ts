export function normalizeNumber(value: string | number, defaultValue = 0) {
  const number = Number(value);

  if (isNaN(number)) {
    return defaultValue;
  }

  return number;
}

export function toFixed(value: number, digitsAfterDot = 2) {
  const string = String(value);

  const dotIndex = string.indexOf(".");

  if (dotIndex === -1) {
    return value;
  }

  if (string.length - dotIndex - 1 <= digitsAfterDot) {
    return value;
  }

  return string.slice(0, dotIndex + digitsAfterDot + 1);
}

export function normalizeNumberToTwoDecimals(value: string | number) {
  return Number(toFixed(normalizeNumber(value), 2));
}
