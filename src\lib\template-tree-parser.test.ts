import { parseTree } from "./template-tree-parser";
import { evaluateTree } from "./template-tree-evaluator";

describe("Template Tree Parser - New Features", () => {
  describe("Boolean Expressions (AND/OR)", () => {
    test("simple AND expression", () => {
      const tree = parseTree("{a AND b}");
      expect(tree).toEqual({
        type: "boolean",
        operator: "AND",
        operands: [
          { type: "reference", name: "a" },
          { type: "reference", name: "b" }
        ]
      });
    });

    test("simple OR expression", () => {
      const tree = parseTree("{a OR b}");
      expect(tree).toEqual({
        type: "boolean",
        operator: "OR",
        operands: [
          { type: "reference", name: "a" },
          { type: "reference", name: "b" }
        ]
      });
    });

    test("multiple AND operands", () => {
      const tree = parseTree("{a AND b AND c}");
      expect(tree).toEqual({
        type: "boolean",
        operator: "AND",
        operands: [
          { type: "reference", name: "a" },
          { type: "reference", name: "b" },
          { type: "reference", name: "c" }
        ]
      });
    });

    test("multiple OR operands", () => {
      const tree = parseTree("{a OR b OR c}");
      expect(tree).toEqual({
        type: "boolean",
        operator: "OR",
        operands: [
          { type: "reference", name: "a" },
          { type: "reference", name: "b" },
          { type: "reference", name: "c" }
        ]
      });
    });

    test("nested boolean expressions", () => {
      const tree = parseTree("{a AND {b OR c}}");
      expect(tree).toEqual({
        type: "boolean",
        operator: "AND",
        operands: [
          { type: "reference", name: "a" },
          {
            type: "boolean",
            operator: "OR",
            operands: [
              { type: "reference", name: "b" },
              { type: "reference", name: "c" }
            ]
          }
        ]
      });
    });

    test("AND has higher precedence than OR", () => {
      const tree = parseTree("{a OR b AND c}");
      expect(tree).toEqual({
        type: "boolean",
        operator: "OR",
        operands: [
          { type: "reference", name: "a" },
          {
            type: "boolean",
            operator: "AND",
            operands: [
              { type: "reference", name: "b" },
              { type: "reference", name: "c" }
            ]
          }
        ]
      });
    });
  });

  describe("Plus Expressions", () => {
    test("simple plus expression", () => {
      const tree = parseTree('{"hello" + "world"}');
      expect(tree).toEqual({
        type: "plus",
        operands: [
          { type: "value", value: "hello" },
          { type: "value", value: "world" }
        ]
      });
    });

    test("multiple plus operands", () => {
      const tree = parseTree('{"a" + "b" + "c"}');
      expect(tree).toEqual({
        type: "plus",
        operands: [
          { type: "value", value: "a" },
          { type: "value", value: "b" },
          { type: "value", value: "c" }
        ]
      });
    });

    test("plus with references", () => {
      const tree = parseTree('{name + " " + surname}');
      expect(tree).toEqual({
        type: "plus",
        operands: [
          { type: "reference", name: "name" },
          { type: "value", value: " " },
          { type: "reference", name: "surname" }
        ]
      });
    });
  });

  describe("TreeNode as Ternary Condition", () => {
    test("boolean expression as ternary condition", () => {
      const tree = parseTree('{{a AND b} ? "yes" : "no"}');
      expect(tree).toEqual({
        type: "ternary",
        condition: {
          type: "boolean",
          operator: "AND",
          operands: [
            { type: "reference", name: "a" },
            { type: "reference", name: "b" }
          ]
        },
        true: { type: "value", value: "yes" },
        false: { type: "value", value: "no" }
      });
    });

    test("plus expression as ternary condition", () => {
      const tree = parseTree('{{a + b} ? "concatenated" : "empty"}');
      expect(tree).toEqual({
        type: "ternary",
        condition: {
          type: "plus",
          operands: [
            { type: "reference", name: "a" },
            { type: "reference", name: "b" }
          ]
        },
        true: { type: "value", value: "concatenated" },
        false: { type: "value", value: "empty" }
      });
    });
  });

  describe("Operator Mixing Validation", () => {
    test("cannot mix boolean and plus operators", () => {
      expect(() => parseTree("{a AND b + c}")).toThrow(/Cannot mix AND operator with PLUS/);
    });

    test("cannot mix plus and OR operators", () => {
      expect(() => parseTree('{"a" + "b" OR c}')).toThrow(/Cannot mix \+ operator with OR/);
    });

    test("can use operators in nested expressions", () => {
      expect(() => parseTree('{{a AND b} + {c OR d}}')).not.toThrow();
    });
  });
});

describe("Template Tree Evaluator - New Features", () => {
  describe("Boolean Expression Evaluation", () => {
    test("AND expression - all truthy", () => {
      const tree = parseTree("{a AND b}");
      const result = evaluateTree({ a: "value1", b: "value2" }, tree);
      expect(result).toBe("true");
    });

    test("AND expression - one falsy", () => {
      const tree = parseTree("{a AND b}");
      const result = evaluateTree({ a: "value1", b: "" }, tree);
      expect(result).toBe(null);
    });

    test("OR expression - one truthy", () => {
      const tree = parseTree("{a OR b}");
      const result = evaluateTree({ a: "", b: "value2" }, tree);
      expect(result).toBe("true");
    });

    test("OR expression - all falsy", () => {
      const tree = parseTree("{a OR b}");
      const result = evaluateTree({ a: "", b: null }, tree);
      expect(result).toBe(null);
    });
  });

  describe("Plus Expression Evaluation", () => {
    test("concatenates strings", () => {
      const tree = parseTree('{"hello" + " " + "world"}');
      const result = evaluateTree({}, tree);
      expect(result).toBe("hello world");
    });

    test("converts values to strings", () => {
      const tree = parseTree('{name + " " + age}');
      const result = evaluateTree({ name: "John", age: 25 }, tree);
      expect(result).toBe("John 25");
    });

    test("handles null values", () => {
      const tree = parseTree('{name + " " + missing}');
      const result = evaluateTree({ name: "John" }, tree);
      expect(result).toBe("John ");
    });
  });

  describe("TreeNode Ternary Condition Evaluation", () => {
    test("boolean condition - truthy", () => {
      const tree = parseTree('{{a AND b} ? "yes" : "no"}');
      const result = evaluateTree({ a: "value1", b: "value2" }, tree);
      expect(result).toBe("yes");
    });

    test("boolean condition - falsy", () => {
      const tree = parseTree('{{a AND b} ? "yes" : "no"}');
      const result = evaluateTree({ a: "value1", b: "" }, tree);
      expect(result).toBe("no");
    });

    test("plus condition - truthy", () => {
      const tree = parseTree('{{a + b} ? "concatenated" : "empty"}');
      const result = evaluateTree({ a: "hello", b: "world" }, tree);
      expect(result).toBe("concatenated");
    });

    test("plus condition - falsy", () => {
      const tree = parseTree('{{a + b} ? "concatenated" : "empty"}');
      const result = evaluateTree({}, tree);
      expect(result).toBe("empty");
    });
  });
});
