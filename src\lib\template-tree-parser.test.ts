import { parseTree } from "./template-tree-parser";
import { evaluateTree } from "./template-tree-evaluator";

// Simple test framework
function assert(condition: boolean, message: string) {
  if (!condition) {
    throw new Error(`Assertion failed: ${message}`);
  }
}

function assertEqual(actual: any, expected: any, message: string) {
  if (JSON.stringify(actual) !== JSON.stringify(expected)) {
    console.error("Expected:", JSON.stringify(expected, null, 2));
    console.error("Actual:", JSON.stringify(actual, null, 2));
    throw new Error(`Assertion failed: ${message}`);
  }
}

function assertThrows(fn: () => void, expectedError: RegExp, message: string) {
  try {
    fn();
    throw new Error(`Expected function to throw, but it didn't: ${message}`);
  } catch (error) {
    if (!expectedError.test((error as Error).message)) {
      throw new Error(`Expected error to match ${expectedError}, but got: ${(error as Error).message}`);
    }
  }
}

function runTest(name: string, testFn: () => void) {
  try {
    testFn();
    console.log(`✓ ${name}`);
  } catch (error) {
    console.error(`✗ ${name}: ${(error as Error).message}`);
  }
}

console.log("Running Template Tree Parser Tests...\n");

// Boolean Expressions (AND/OR)
runTest("simple AND expression", () => {
  const tree = parseTree("{a AND b}");
  assertEqual(tree, {
    type: "boolean",
    operator: "AND",
    operands: [
      { type: "reference", name: "a" },
      { type: "reference", name: "b" }
    ]
  }, "AND expression should parse correctly");
});

runTest("simple OR expression", () => {
  const tree = parseTree("{a OR b}");
  assertEqual(tree, {
    type: "boolean",
    operator: "OR",
    operands: [
      { type: "reference", name: "a" },
      { type: "reference", name: "b" }
    ]
  }, "OR expression should parse correctly");
});

runTest("multiple AND operands", () => {
  const tree = parseTree("{a AND b AND c}");
  assertEqual(tree, {
    type: "boolean",
    operator: "AND",
    operands: [
      { type: "reference", name: "a" },
      { type: "reference", name: "b" },
      { type: "reference", name: "c" }
    ]
  }, "Multiple AND operands should parse correctly");
});

runTest("multiple OR operands", () => {
  const tree = parseTree("{a OR b OR c}");
  assertEqual(tree, {
    type: "boolean",
    operator: "OR",
    operands: [
      { type: "reference", name: "a" },
      { type: "reference", name: "b" },
      { type: "reference", name: "c" }
    ]
  }, "Multiple OR operands should parse correctly");
});

runTest("nested boolean expressions", () => {
  const tree = parseTree("{a AND {b OR c}}");
  assertEqual(tree, {
    type: "boolean",
    operator: "AND",
    operands: [
      { type: "reference", name: "a" },
      {
        type: "boolean",
        operator: "OR",
        operands: [
          { type: "reference", name: "b" },
          { type: "reference", name: "c" }
        ]
      }
    ]
  }, "Nested boolean expressions should parse correctly");
});

runTest("AND has higher precedence than OR", () => {
  const tree = parseTree("{a OR b AND c}");
  assertEqual(tree, {
    type: "boolean",
    operator: "OR",
    operands: [
      { type: "reference", name: "a" },
      {
        type: "boolean",
        operator: "AND",
        operands: [
          { type: "reference", name: "b" },
          { type: "reference", name: "c" }
        ]
      }
    ]
  }, "AND should have higher precedence than OR");
});

// Plus Expressions
runTest("simple plus expression", () => {
  const tree = parseTree('{"hello" + "world"}');
  assertEqual(tree, {
    type: "plus",
    operands: [
      { type: "value", value: "hello" },
      { type: "value", value: "world" }
    ]
  }, "Simple plus expression should parse correctly");
});

runTest("multiple plus operands", () => {
  const tree = parseTree('{"a" + "b" + "c"}');
  assertEqual(tree, {
    type: "plus",
    operands: [
      { type: "value", value: "a" },
      { type: "value", value: "b" },
      { type: "value", value: "c" }
    ]
  }, "Multiple plus operands should parse correctly");
});

runTest("plus with references", () => {
  const tree = parseTree('{name + " " + surname}');
  assertEqual(tree, {
    type: "plus",
    operands: [
      { type: "reference", name: "name" },
      { type: "value", value: " " },
      { type: "reference", name: "surname" }
    ]
  }, "Plus with references should parse correctly");
});

// TreeNode as Ternary Condition
runTest("boolean expression as ternary condition", () => {
  const tree = parseTree('{{a AND b} ? "yes" : "no"}');
  assertEqual(tree, {
    type: "ternary",
    condition: {
      type: "boolean",
      operator: "AND",
      operands: [
        { type: "reference", name: "a" },
        { type: "reference", name: "b" }
      ]
    },
    true: { type: "value", value: "yes" },
    false: { type: "value", value: "no" }
  }, "Boolean expression as ternary condition should parse correctly");
});

runTest("plus expression as ternary condition", () => {
  const tree = parseTree('{{a + b} ? "concatenated" : "empty"}');
  assertEqual(tree, {
    type: "ternary",
    condition: {
      type: "plus",
      operands: [
        { type: "reference", name: "a" },
        { type: "reference", name: "b" }
      ]
    },
    true: { type: "value", value: "concatenated" },
    false: { type: "value", value: "empty" }
  }, "Plus expression as ternary condition should parse correctly");
});

// Operator Mixing Validation
runTest("cannot mix boolean and plus operators", () => {
  assertThrows(
    () => parseTree("{a AND b + c}"),
    /Cannot mix AND operator with PLUS/,
    "Should throw error when mixing AND and PLUS"
  );
});

runTest("cannot mix plus and OR operators", () => {
  assertThrows(
    () => parseTree('{"a" + "b" OR c}'),
    /Cannot mix \+ operator with OR/,
    "Should throw error when mixing PLUS and OR"
  );
});

runTest("can use operators in nested expressions", () => {
  // This should not throw
  const tree = parseTree('{{a AND b} + {c OR d}}');
  assert(tree.type === "plus", "Should allow operators in nested expressions");
});

console.log("\nRunning Template Tree Evaluator Tests...\n");

// Boolean Expression Evaluation
runTest("AND expression - all truthy", () => {
  const tree = parseTree("{a AND b}");
  const result = evaluateTree({ a: "value1", b: "value2" }, tree);
  assertEqual(result, "true", "AND with all truthy values should return 'true'");
});

runTest("AND expression - one falsy", () => {
  const tree = parseTree("{a AND b}");
  const result = evaluateTree({ a: "value1", b: "" }, tree);
  assertEqual(result, null, "AND with one falsy value should return null");
});

runTest("OR expression - one truthy", () => {
  const tree = parseTree("{a OR b}");
  const result = evaluateTree({ a: "", b: "value2" }, tree);
  assertEqual(result, "true", "OR with one truthy value should return 'true'");
});

runTest("OR expression - all falsy", () => {
  const tree = parseTree("{a OR b}");
  const result = evaluateTree({ a: "", b: null }, tree);
  assertEqual(result, null, "OR with all falsy values should return null");
});

// Plus Expression Evaluation
runTest("concatenates strings", () => {
  const tree = parseTree('{"hello" + " " + "world"}');
  const result = evaluateTree({}, tree);
  assertEqual(result, "hello world", "Plus should concatenate strings");
});

runTest("converts values to strings", () => {
  const tree = parseTree('{name + " " + age}');
  const result = evaluateTree({ name: "John", age: 25 }, tree);
  assertEqual(result, "John 25", "Plus should convert values to strings");
});

runTest("handles null values", () => {
  const tree = parseTree('{name + " " + missing}');
  const result = evaluateTree({ name: "John" }, tree);
  assertEqual(result, "John ", "Plus should handle null values as empty strings");
});

// TreeNode Ternary Condition Evaluation
runTest("boolean condition - truthy", () => {
  const tree = parseTree('{{a AND b} ? "yes" : "no"}');
  const result = evaluateTree({ a: "value1", b: "value2" }, tree);
  assertEqual(result, "yes", "Ternary with truthy boolean condition should return true branch");
});

runTest("boolean condition - falsy", () => {
  const tree = parseTree('{{a AND b} ? "yes" : "no"}');
  const result = evaluateTree({ a: "value1", b: "" }, tree);
  assertEqual(result, "no", "Ternary with falsy boolean condition should return false branch");
});

runTest("plus condition - truthy", () => {
  const tree = parseTree('{{a + b} ? "concatenated" : "empty"}');
  const result = evaluateTree({ a: "hello", b: "world" }, tree);
  assertEqual(result, "concatenated", "Ternary with truthy plus condition should return true branch");
});

runTest("plus condition - falsy", () => {
  const tree = parseTree('{{a + b} ? "concatenated" : "empty"}');
  const result = evaluateTree({}, tree);
  assertEqual(result, "empty", "Ternary with falsy plus condition should return false branch");
});

console.log("\nAll tests completed!");
