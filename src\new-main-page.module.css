.header {
  display: flex;
  margin: 35px 16px 51px;
  justify-content: space-between;
}

.container {
  display: flex;
  justify-content: end;
  gap: 16px;
  flex-shrink: 1;
  width: 100%;
}


.base {
  width: 30px;
  height: 30px;
  visibility: visible;
  align-items: center;
}

/* When Sidebar is Open (AppBar Hidden) */
.open {
  width: 30px;
  height: 30px;
  margin-left: -200px;
  visibility: hidden;
}

/* When Sidebar is Closed (AppBar Visible) */
.visible {
  width: 30px;
  height: 30px;
  margin-left: 0;
  visibility: visible;
  transition: margin-left 0.1s ease-in-out, width 0.5s ease-in-out, height 0.5s ease-in-out;
}

.contentContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-shrink: 1;
  gap: 16px;
}

form {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.textContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.textInputsContainer {
  display: flex;
  gap: 16px;
  width: 100%;
}

.label {
  font-weight: 700;
  font-size: 16px;
  margin-bottom: 7px;
  align-self: start;
}

.sendButton {
  box-shadow: 0px 4px 4px 0px #00000040;
  background: #70B57D;
  width: 43px;
  height: 43px;
  border-radius: 50%;
  position: absolute;
  z-index: 1;

  display: flex;
  justify-content: center;
  align-items: center;
}

.sendButton:disabled {
  box-shadow: none;
  cursor: not-allowed;
}

.resultTextContainer {
  display: flex;
  flex-direction: column;
  min-width: calc(50% - 8px);
  flex-grow: 1;
  white-space: pre-line;
  flex-shrink: 1;
}

.titleResult {
  max-width: 100%;
  flex-shrink: 0;
  width: 100%;
  height: 36px;
  border-radius: 10px;
  background: #F5F5F5;
  display: flex;
  align-items: center;
  padding: 0 7px 0 24px;
  font-size: 12px;
  font-weight: 700;
  overflow-y: auto;
  line-height: 16px;
  justify-content: space-between;
  white-space: nowrap;
}

.titleResult>button {
  align-self: end;
}

.fullTextResultContainer {
  width: 100%;
  height: 135px;
  border-radius: 10px;
  flex-shrink: 1;
  border-width: 1px;
  background: #F5F5F5;
  padding: 9px 7px 9px 24px;
  margin-top: 7px;
  box-sizing: border-box;

  display: flex;
  flex-direction: row;
  justify-content: space-between;
  position: relative;
}

.fullTextResult {
  overflow-x: auto;
  font-size: 12px;
  font-weight: 400;
  line-height: 140%;
  word-break: break-all;
}

.actionBtns {
  align-self: end;
}

.textIntoInputContainer {
  position: relative;
  min-width: calc(50% - 8px);
  flex-shrink: 1;
  flex-grow: 1;
}

.textFieldContainer {
  width: 100%;
  height: 177px;
  position: relative;
}

.textField {
  width: 100%;
  height: 177px;
  border-radius: 10px;
  border: 1px solid #C6C5CA;
  padding: 14px 26px 14px 24px;
  resize: none;
  box-sizing: border-box;
  font-size: 12px;
  font-weight: 400;
  line-height: 140%;
}

.textField:focus-visible {
  outline: none;
}

.fileUploadContainer {
  position: absolute;
  bottom: 14px;
  right: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.fileGallery {
  display: flex;
  gap: 6px;
  align-items: center;
}

.fileItem {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: white;
  border: 1px solid #C6C5CA;
  border-radius: 4px;
  padding: 4px;
  min-width: 40px;
  max-width: 60px;
  cursor: pointer;
}

.fileExtension {
  font-size: 8px;
  font-weight: 600;
  color: #467c8d;
  line-height: 1;
  margin-bottom: 2px;
}

.fileName {
  font-size: 7px;
  color: #666;
  text-align: center;
  word-break: break-all;
  line-height: 1.1;
  max-height: 14px;
  overflow: hidden;
}

.fileRemoveButton {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #EB4E3D;
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  line-height: 1;
}

.fileUploadButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #70B57D;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: background-color 0.2s;
  margin: 0;
  padding: 0;
}

.fileUploadButton:hover {
  background: #f5f5f5;
}

.hiddenFileInput {
  display: none;
}

.addressesInfoContainer {
  display: flex;
  justify-content: center;
  gap: 16px;

  width: 100%;

  flex-shrink: 1;
  flex-grow: 1;
}

.addressesInfoContainer {
  display: flex;
  justify-content: center;
  gap: 16px;

  width: 100%;

  flex-shrink: 1;
  flex-grow: 1;
}

.additionalDetailsContainer {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  flex-shrink: 1;
  width: 100%;
}

.inputsContainer {
  display: grid;
  grid-template-columns: repeat(6, minmax(0, 1fr));
  gap: 16px;
}

.inputsContainer>div:nth-child(1) {
  grid-column: span 2;
}

.deliveryInfoContainer {
  border: 1px solid #C6C5CA;
  flex-basis: 392px;
  border-radius: 10px;
  padding: 0 27px;

  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-shrink: 1;
  gap: 16px;
  flex-grow: 1;
}

.deliveryInfoContainer>div {
  display: flex;
  gap: 16px;
}


.shipmentSummaryContainer {
  padding-right: 204px;
  width: 100%;
}

.shipmentSummaryLabel {
  font-size: 16px;
}

.inputsContainer {
  display: flex;
  flex-grow: 1;
  flex-shrink: 1;
  width: 100%;
  gap: 16px;
}

.inputsContainer>div {
  flex-shrink: 1;
  flex-grow: 1;
  width: 100%;
}

.packageDetailsContainer {
  width: 100%;
}

.packageDetailsSectionsContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.addPackageButton {
  font-size: 12px;
  align-self: start;
  font-weight: 700;
  line-height: 16px;
}