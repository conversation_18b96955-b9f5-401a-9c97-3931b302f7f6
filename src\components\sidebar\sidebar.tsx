import { Drawer, <PERSON><PERSON><PERSON><PERSON>on } from "@mui/material";
import { Icon, LogoText } from "./assets";
import styles from "./sidebar.module.css";
import { JSX } from "react";
import { Link, useLocation } from "react-router";

const sections: {
  label: string;
  icon: JSX.Element;
  href?: string;
}[] = [
    {
      label: "Create request",
      icon: <Icon.CreateRequest />,
      href: "/",
    },
    {
      label: "My requests",
      icon: <Icon.Folder />,
      href: "/requests",
    },
    {
      label: "Dashboard",
      icon: <Icon.Dashboard />,
    },
    {
      label: "Settings",
      icon: <Icon.Settings />,
    },
  ];

type Props = {
  handleSidebarClose: (state: boolean) => void;
  open: boolean;
};

export const Sidebar = ({ handleSidebarClose, open }: Props) => {
  const location = useLocation();

  return (
    <Drawer
      open={open}
      variant="persistent"
      anchor="left"
      sx={{
        width: 190,
        flexShrink: 0,
        "& .MuiDrawer-paper": {
          width: 190,
          boxSizing: "border-box",
        },
      }}
    >
      <aside className={styles.sidebar}>
        <div className={styles.logoContainer}>
          <img src="assets/Logo.svg" />
          <LogoText />
          <IconButton
            edge="end"
            disableRipple
            onClick={() => handleSidebarClose(false)}
            sx={{
              alignSelf: "end",
            }}
          >
            <Icon.Sidebar />
          </IconButton>
        </div>

        <ul className={styles.sections}>
          {/* {sections.map((section) => (
            <li
              key={section.label}
              className={`${styles.section} ${section?.selected ? styles.section__selected : undefined
                }`}
            >
              {section.href ? (
                <Link to={section.href}>
                  {section.icon}
                  {section.label}
                </Link>
              ) : (
                <>
                  {section.icon}
                  {section.label}
                </>
              )}
            </li>
          ))} */}

          {sections.map(section => {
            if (section.href) {
              const isSelected = location.pathname === section.href;

              return (
                <li
                  key={section.label}
                >
                  <Link
                    to={section.href}
                    className={`${styles.section} ${isSelected ? styles.section__selected : undefined}`}
                  >
                    {section.icon}
                    {section.label}
                  </Link>
                </li>
              );
            }

            return (
              <li
                key={section.label}
                className={styles.section}
              >
                {section.icon}
                {section.label}
              </li>
            )
          })}
        </ul>
      </aside>
    </Drawer>
  );
};
