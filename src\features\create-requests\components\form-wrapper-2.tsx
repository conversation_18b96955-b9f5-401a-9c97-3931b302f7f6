import { forwardRef, memo, MouseEvent, useCallback, useEffect, useImperativeHandle, useState } from "react";
import { useForm, FormProvider, useWatch, useFormContext, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as Lists from "../../../shared/lists";
import { ControlledCustomSelect, ControlledTextField, CustomAlert, TextFieldWithLabel } from "../../../components";
import { Detail, FormData, ShippingInfo, Summary, SummaryFieldKey } from "../types/shippingTypes";
import { defaultShippingInfo } from "../utils/consts";
import { generateResultText } from "../utils/generateResultText";
import { ShippingSchemaWithValidation } from "../utils/validationSchema";
import { useSaveRequest } from "../data/useSaveRequest";
import styles from "./form-wrapper-2.module.css";
import {
  DELIVERY_REQUIRED_INCOTERMS,
  PICKUP_REQUIRED_INCOTERMS,
} from "../utils/consts";

import {
  TResultText,
  AddressInfo,
  GenericErrorHelper,
  PackageItem,
} from ".";
import useSubscribeToPackageUpdates from "../utils/useSubscribeToPackageUpdates";
import useSubscribeToWeightAndVolumeUpdates from "../utils/useSubscribeToWeightAndVolumeUpdates";
import { calculateTotalDensity, getTotalFieldsSum } from "../utils/calculateTotalPackageDetails";

type Props = {
  data: ShippingInfo | undefined;
  setResultText: (text: TResultText) => void;
};

const defaultPackage: Detail = {
  piece: 1,
  package_type: "",
  dimension: {
    width: 0,
    height: 0,
    length: 0,
    volume: 0,
    weight: 0,
    is_stackable: false,
  },
};

export const FormWrapper2 = memo(forwardRef(({ data, setResultText }: Props, ref) => {
  const methods = useForm<FormData>({
    resolver: zodResolver(ShippingSchemaWithValidation),
    defaultValues: defaultShippingInfo,
    mode: "onSubmit",
    reValidateMode: "onSubmit",
  });

  const { setValue, watch, setError, trigger } = methods;
  const { saveRequest, success, error: savingError } = useSaveRequest({
    handleFieldErrors: setError,
  });

  watch((data) => {
    const newText = generateResultText(data);
    setResultText(newText);
  });

  useImperativeHandle(ref, () => ({
    submitForm: async () => {
      console.log("submitForm");

      console.dir({ raw: data }, { depth: null });

      if (data) {
        await saveRequest(data);
      }
    },
    reset: () => {
      methods.reset();
    },
  }));

  useEffect(() => {
    if (!data) return;

    Object.keys(data).forEach((key) => {
      setValue(key as keyof ShippingInfo, data[key as keyof ShippingInfo]);
    });
    trigger()
  }, [data, setValue, trigger]);

  return (
    <>
      <CustomAlert message={savingError} severity="error" />
      <CustomAlert message={success} severity="success" />
      <FormProvider {...methods}>
        <form
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              e.preventDefault();
            }
          }}
        >
          <FormContent />
        </form>
      </FormProvider>
    </>
  );
}));

const FormContent = () => {
  const { setValue, getValues, trigger, clearErrors } = useFormContext<FormData>();

  const handleIncotermsChange = (selectedIncoterms?: string) => {
    if (!selectedIncoterms) {
      return;
    }
    clearErrors([
      "incoterms",
      "pickup.city",
      "pickup.address",
      "delivery.address",
      "delivery.city",
    ]);

    if (DELIVERY_REQUIRED_INCOTERMS.includes(selectedIncoterms)) {
      setValue("delivery.is_needed", true);
    }

    if (PICKUP_REQUIRED_INCOTERMS.includes(selectedIncoterms)) {
      setValue("pickup.is_needed", true);
    }

    trigger([
      "pickup.city",
      "delivery.city",
      "delivery.address",
      "pickup.address",
    ]);
  };

  const [base, setBase] = useState({
    piece: 0,
    volume: 0,
    weight: 0,
  });

  const { updateValue } = useSubscribeToPackageUpdates();

  const handleOnBaseChange = useCallback((newBase: number, key: keyof Summary) => {
    setBase((prevValue) => ({
      ...prevValue,
      [key]: newBase < 0 ? 0 : newBase,
    }));
  }, []);

  const handleValueUpdate = useCallback(
    (fieldKey: SummaryFieldKey) => {
      updateValue({
        fieldKey: fieldKey,
        details: getValues("details"),
        base: base,
      });
    },
    [updateValue, base, getValues]
  );

  useSubscribeToWeightAndVolumeUpdates();

  const result = useWatch({
    name: ["summary.weight", "summary.volume"],
  });

  const serviceType = useWatch({
    name: ["service_type"],
  })[0];

  useEffect(() => {
    const density = calculateTotalDensity(
      result[0],
      result[1],
    );

    setValue("summary.density", density);
  }, [result, setValue]);

  const handleChange = useCallback(
    (newSummary: number, key: SummaryFieldKey) => {
      const details = getValues("details");
      const summedDetailsPieces = getTotalFieldsSum(key, details);

      handleOnBaseChange(newSummary - summedDetailsPieces, key);
    },
    [getValues, handleOnBaseChange]
  );

  const { fields, append, remove } = useFieldArray({
    name: "details",
  });
  const { handleRemove, handlePackageAddition } =
    useSubscribeToPackageUpdates();

  const handleAddPackage = (event: MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    append(defaultPackage);
    handlePackageAddition();
  };

  const handlePackageRemove = useCallback(
    (index: number) => {
      remove(index);
      handleRemove(base);
    },
    [remove, handleRemove, base]
  );

  return (
    <>
      <div className={styles.addressesInfoContainer}>
        <div className={styles.deliveryInfoContainer}>
          <div>
            <ControlledCustomSelect
              label="Service"
              options={Lists.services}
              name="service_type"
              handleChange={() => clearErrors("service_type")}
            />
            <ControlledCustomSelect
              label="Incoterms"
              options={Lists.incoterms}
              name="incoterms"
              handleChange={handleIncotermsChange}
            />
          </div>

          <div>
            <ControlledTextField
              label="Origin"
              id="origin"
              name="origin"
              size="small"
              sx={{
                flexGrow: 1,
              }}
            />

            <ControlledTextField
              label="Destination"
              id="destination_country"
              size="small"
              name="destination_country"
              sx={{
                flexGrow: 1,
              }}
            />
          </div>
        </div>

        <AddressInfo
          label="Pick-up address"
          checkboxLabel="yes, we need pick up"
          type="pickup"
        />

        <AddressInfo
          label="Delivery address"
          checkboxLabel="yes, we need address delivery"
          type="delivery"
        />
      </div>

      {/* shipment summary */}
      <div className={styles.shipmentSummaryContainer}>
        <h3 className={styles.shipmentSummaryLabel}>Shipment Summary</h3>

        <div className={styles.inputsContainer}>
          <TextFieldWithLabel
            handleOnBlur={() => trigger("summary.piece")}
            handleOnChange={(result) => handleChange(result, "piece")}
            errorWrapper={
              <GenericErrorHelper
                fieldKey="summary.piece"
                getFieldValue={() =>
                  getTotalFieldsSum("piece", getValues(`details`))
                }
              />
            }
            label="Piece (pcs)"
            formName="summary.piece"
            type="number"
          />
          <TextFieldWithLabel
            label="Total weight (kg)"
            formName="summary.weight"
            handleOnChange={(result) => handleChange(result, "weight")}
            errorWrapper={
              <GenericErrorHelper
                fieldKey="summary.weight"
                getFieldValue={() =>
                  getTotalFieldsSum("weight", getValues(`details`))
                }
              />
            }
            type="number"
            handleOnBlur={() => trigger("summary.weight")}
          />
          <TextFieldWithLabel
            label="Volume (m³)"
            formName="summary.volume"
            type="number"
            handleOnChange={(result) => handleChange(result, "volume")}
            errorWrapper={
              <GenericErrorHelper
                fieldKey="summary.volume"
                getFieldValue={() =>
                  getTotalFieldsSum("volume", getValues(`details`))
                }
              />
            }
            handleOnBlur={() => trigger("summary.volume")}
          />
          <TextFieldWithLabel label="Density" locked formName="summary.density" type="number" />

          {serviceType === "air" && (
            <TextFieldWithLabel
              label="Chargeable weight"
              locked
              type="number"
              formName="summary.chargeable_weight"
            />
          )}
        </div>
      </div>

      {/* package details */}
      <div>
        <h3>Package Details</h3>

        <div className={styles.packageDetailsSectionsContainer}>
          {fields.map((field, index) => (
            <PackageItem
              options={Lists.packageTypes}
              key={field.id}
              index={index}
              remove={handlePackageRemove}
              onChange={({ fieldKey }) => handleValueUpdate(fieldKey)}
            />
          ))}

          <button className={styles.addPackageButton} onClick={handleAddPackage}>
            + Add another piece group
          </button>
        </div>
      </div>

      <div className={styles.additionalDetailsContainer}>
        <h3>Additional Details</h3>

        <div className={styles.inputsContainer}>
          <TextFieldWithLabel
            label="Description of goods"
            formName="additional_details.description_of_goods"
            multiline={true}
            additionalStyling={{
              "& .MuiInputBase-input": { textAlign: "start" },
            }}
          />
          <TextFieldWithLabel
            label="HS codes"
            formName="additional_details.hs_codes"
          />
          <TextFieldWithLabel
            label="Cost of goods"
            formName="additional_details.costs_of_goods"
            type="number"
          />

          <ControlledCustomSelect
            options={Lists.additionalServices}
            withLabel
            label="Additional services"
            name="additional_details.selected_services"
            multiple
            placeholder="Choose services"
          />

          <ControlledCustomSelect
            options={Lists.dangerousGoods}
            withLabel
            label="Dangerous Goods"
            name="additional_details.dangerous_goods"
            multiple
            placeholder="Choose goods"
          />
        </div>
      </div>
    </>
  );
}
